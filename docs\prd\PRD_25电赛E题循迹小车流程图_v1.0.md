# 25电赛E题循迹小车程序流程图需求文档

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-01-02
- **负责人**: Emma (产品经理)
- **项目**: 25电赛E题第一问小车循迹部分程序流程图生成

## 2. 背景与问题陈述

### 2.1 项目背景
- 用户完成了25电赛E题第一问的小车循迹功能实现
- 基于MSPM0G3507微控制器平台开发
- 使用8路灰度传感器进行循迹控制
- 采用PID控制算法实现精确循迹

### 2.2 核心问题
用户需要一份清晰、专业的程序流程图，用于：
- 向评委展示功能完整性和代码逻辑合理性
- 证明循迹功能已完全实现
- 体现代码架构的专业性（尽管算法可能不够完美）

## 3. 目标与成功指标

### 3.1 项目目标
- **主要目标**: 生成简洁明了的程序流程图，字体清晰易读
- **展示目标**: 让评委清楚看出功能已完成且代码逻辑合理
- **格式目标**: 优先生成PDF格式，HTML和JPG格式作为备选

### 3.2 关键结果(KRs)
- KR1: 流程图包含所有核心功能模块（初始化、传感器读取、PID控制、电机驱动）
- KR2: 字体大小适中，确保清晰可读
- KR3: 逻辑流程清晰，体现循迹控制的完整性
- KR4: 生成高质量的可视化文档（PDF/HTML/JPG）

### 3.3 反向指标
- 避免流程图过于复杂难懂
- 避免字体过小影响可读性
- 避免遗漏关键功能模块

## 4. 用户画像与用户故事

### 4.1 目标用户
- **主要用户**: 25电赛评委
- **次要用户**: 参赛团队成员、指导老师

### 4.2 用户故事
- 作为评委，我希望快速理解循迹小车的程序逻辑和功能完整性
- 作为参赛者，我希望通过流程图展示我的技术实现能力
- 作为指导老师，我希望看到学生的代码架构思路

## 5. 功能规格详述

### 5.1 核心功能模块分析

#### 5.1.1 系统初始化模块
- **功能**: 系统启动和硬件初始化
- **关键组件**: 
  - PID参数初始化 (`PID_Init()`)
  - 系统配置初始化 (`SYSCFG_DL_init()`)
  - 定时器和中断配置

#### 5.1.2 传感器数据采集模块
- **功能**: 8路灰度传感器数据读取和处理
- **关键组件**:
  - GPIO引脚读取 (`Gray_Task()`)
  - 数字量组合和取反处理
  - 加权平均算法计算位置误差

#### 5.1.3 PID控制算法模块
- **功能**: 基于位置误差的PID控制计算
- **关键组件**:
  - 位置式PID计算 (`pid_calculate_positional()`)
  - 状态机处理（直角转弯检测）
  - 输出限幅处理

#### 5.1.4 电机驱动控制模块
- **功能**: 双电机差速控制实现循迹
- **关键组件**:
  - PWM信号生成
  - 电机方向控制
  - 速度设置 (`Set_Speed()`)

#### 5.1.5 按键控制模块
- **功能**: 用户交互和圈数控制
- **关键组件**:
  - 按键扫描 (`key_scan()`)
  - 圈数计数和停车控制

### 5.2 程序执行流程

#### 5.2.1 主循环结构
```
主函数 -> 初始化 -> 无限循环等待中断
定时器中断 -> 传感器读取 -> PID计算 -> 电机控制
```

#### 5.2.2 状态机逻辑
- **FIRST**: 初始状态，检测第一个直角
- **NORMAL**: 正常循迹状态
- **TURNING**: 转弯状态（减速处理）
- **RECOVER**: 恢复状态（速度恢复）

## 6. 范围定义

### 6.1 包含功能(In Scope)
- 系统初始化流程
- 传感器数据采集和处理流程
- PID控制算法流程
- 电机驱动控制流程
- 状态机转换逻辑
- 按键控制和圈数管理

### 6.2 排除功能(Out of Scope)
- 详细的硬件连接图
- 具体的PID参数调试过程
- UART通信和OLED显示功能（已注释）
- MPU6050陀螺仪功能（已注释）

## 7. 依赖与风险

### 7.1 内部依赖项
- 需要分析现有代码结构
- 需要理解PID控制逻辑
- 需要掌握状态机设计思路

### 7.2 外部依赖项
- Mermaid流程图生成工具
- PDF/HTML/JPG格式转换工具

### 7.3 潜在风险
- 代码逻辑理解可能不够深入
- 流程图可能过于简化或复杂
- 格式转换可能影响清晰度

## 8. 技术实现要求

### 8.1 流程图设计原则
- **简洁性**: 突出核心逻辑，避免过度细节
- **清晰性**: 字体大小适中，连接线清晰
- **完整性**: 包含所有关键功能模块
- **专业性**: 体现良好的代码架构思维

### 8.2 格式要求
- **优先格式**: PDF（矢量图形，缩放不失真）
- **备选格式**: HTML（交互性好）、JPG（兼容性强）
- **字体要求**: 足够大以确保清晰可读
- **布局要求**: 合理的模块分布和连接关系

## 9. 验收标准

### 9.1 功能完整性
- [ ] 包含系统初始化流程
- [ ] 包含传感器数据处理流程  
- [ ] 包含PID控制算法流程
- [ ] 包含电机驱动控制流程
- [ ] 包含状态机转换逻辑

### 9.2 可读性标准
- [ ] 字体大小清晰可读
- [ ] 模块划分合理清晰
- [ ] 流程连接逻辑正确
- [ ] 整体布局美观专业

### 9.3 格式标准
- [ ] 成功生成PDF格式文件
- [ ] 备选格式可正常查看
- [ ] 文件大小适中便于分享

## 10. 发布计划

### 10.1 开发阶段
1. **代码分析阶段** (已完成): 深入理解现有代码结构
2. **流程设计阶段** (进行中): 设计流程图结构和内容
3. **图形生成阶段** (待开始): 使用工具生成流程图
4. **格式转换阶段** (待开始): 生成多种格式文件
5. **质量验收阶段** (待开始): 检查清晰度和完整性

### 10.2 交付时间线
- 总预计时间: 15分钟
- 当前进度: 需求分析完成，准备进入技术设计阶段

---

**文档状态**: ✅ 已完成
**下一步**: 转交Bob进行技术架构设计和流程图结构规划