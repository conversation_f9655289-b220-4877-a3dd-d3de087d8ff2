# 25电赛E题循迹小车程序流程图文档

## 📋 文档概述

本文档包含25电赛E题第一问循迹小车的完整程序流程图，展示了基于MSPM0G3507微控制器的8路灰度传感器循迹控制系统。

## 📁 文档结构

```
docs/
├── README.md                           # 本文档
├── prd/                               # 产品需求文档
│   └── PRD_25电赛E题循迹小车流程图_v1.0.md
├── architecture/                      # 技术架构文档
│   └── Architecture_循迹小车流程图_v1.0.md
└── development/                       # 开发文档和流程图
    ├── 循迹小车程序流程图.md           # Markdown格式流程图
    └── 循迹小车程序流程图.html         # HTML格式流程图
```

## 🎯 快速使用指南

### 方式一：查看HTML版本（推荐）
1. 双击打开 `docs/development/循迹小车程序流程图.html`
2. 在浏览器中查看完整的交互式流程图
3. 支持缩放、打印和导出功能

### 方式二：查看Markdown版本
1. 使用支持Mermaid的编辑器（如VS Code、Typora）
2. 打开 `docs/development/循迹小车程序流程图.md`
3. 查看流程图和详细说明

### 方式三：在线编辑
1. 复制Markdown文件中的Mermaid代码
2. 访问 https://mermaid.live/
3. 粘贴代码进行在线查看和编辑

## 📤 导出格式说明

### PDF格式（推荐用于提交）
- **优势**: 矢量图形，缩放不失真，适合正式文档
- **导出方法**: 
  1. 打开HTML文件
  2. 浏览器菜单 → 打印 → 另存为PDF
  3. 设置页面为A4横向，边距适中

### JPG/PNG格式（推荐用于PPT）
- **优势**: 兼容性强，便于插入演示文稿
- **导出方法**:
  1. 打开HTML文件
  2. 右键点击流程图区域
  3. 选择"另存为图像"

### HTML格式（推荐用于演示）
- **优势**: 交互性好，支持缩放和在线查看
- **使用方法**: 直接分享HTML文件或部署到网页

## 🔧 技术特点展示

### 核心功能模块
- ✅ **系统初始化**: PID参数、系统配置、中断设置
- ✅ **传感器处理**: 8路GPIO读取、数据处理、位置误差计算
- ✅ **PID控制**: 状态机管理、算法计算、输出限幅
- ✅ **电机控制**: 速度设置、PWM控制、方向控制
- ✅ **用户交互**: 按键扫描、圈数控制

### 状态机设计
- **FIRST**: 初始状态，等待第一个直角转弯
- **NORMAL**: 正常循迹状态，基础速度30
- **TURNING**: 转弯状态，减速至20
- **RECOVER**: 恢复状态，延时后恢复正常速度

### 关键算法
- **加权平均**: 8路传感器加权计算位置误差
- **位置式PID**: 精确的循迹控制算法
- **差速控制**: 左右轮速度差实现精确转向
- **中断驱动**: 高效的实时控制架构

## 🎨 流程图设计特点

### 视觉设计
- **颜色编码**: 不同功能模块使用不同颜色区分
- **字体大小**: 16px，确保清晰可读
- **布局合理**: 垂直流程，逻辑清晰
- **分组明确**: 使用子图组织相关功能

### 专业性体现
- **完整性**: 包含所有核心功能模块
- **逻辑性**: 体现完整的程序执行流程
- **技术性**: 突出关键的控制算法和状态管理
- **可读性**: 适合向评委展示技术实现

## 💡 使用建议

### 向评委展示时
1. **优先使用HTML版本**: 交互性好，可以缩放查看细节
2. **准备PDF备份**: 防止设备兼容性问题
3. **重点说明**: 状态机设计和PID控制算法
4. **强调完整性**: 所有核心功能都已实现

### 技术交流时
1. **参考架构文档**: 了解详细的技术设计思路
2. **查看源代码映射**: 流程图与实际代码的对应关系
3. **讨论优化点**: 基于流程图分析可能的改进方向

## 📞 技术支持

如需修改流程图或有技术问题，请参考：
- **需求文档**: `prd/PRD_25电赛E题循迹小车流程图_v1.0.md`
- **架构文档**: `architecture/Architecture_循迹小车流程图_v1.0.md`
- **Mermaid官方文档**: https://mermaid.js.org/

---

**文档版本**: v1.0  
**生成日期**: 2025-01-02  
**技术团队**: Mike(领导) + Emma(需求) + Bob(架构) + Alex(实现)  
**质量保证**: 字体清晰、逻辑完整、专业展示