<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>25电赛E题循迹小车程序流程图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
        }
        .info-section {
            background-color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        .info-item {
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .info-item strong {
            color: #2c3e50;
        }
        .diagram-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background-color: #fafafa;
            border-radius: 8px;
            border: 2px dashed #bdc3c7;
        }
        .legend {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
            padding: 15px;
            background-color: #ecf0f1;
            border-radius: 8px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
            border: 2px solid #333;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .feature-card {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #27ae60;
        }
        .feature-card h3 {
            color: #27ae60;
            margin-top: 0;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 5px 0;
            position: relative;
            padding-left: 20px;
        }
        .feature-list li:before {
            content: "✓";
            color: #27ae60;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        .print-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        @media print {
            body { background-color: white; }
            .container { box-shadow: none; }
            .print-info { display: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>25电赛E题循迹小车程序流程图</h1>
        
        <div class="info-section">
            <div class="info-grid">
                <div class="info-item">
                    <strong>项目名称:</strong> 25电赛E题第一问循迹小车
                </div>
                <div class="info-item">
                    <strong>开发平台:</strong> MSPM0G3507微控制器
                </div>
                <div class="info-item">
                    <strong>传感器:</strong> 8路灰度传感器
                </div>
                <div class="info-item">
                    <strong>控制算法:</strong> PID控制 + 状态机
                </div>
                <div class="info-item">
                    <strong>生成日期:</strong> 2025-01-02
                </div>
                <div class="info-item">
                    <strong>文档版本:</strong> v1.0
                </div>
            </div>
        </div>

        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #e1f5fe;"></div>
                <span>系统初始化</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #f3e5f5;"></div>
                <span>传感器处理</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #e8f5e8;"></div>
                <span>PID控制</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff3e0;"></div>
                <span>电机控制</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #ffebee;"></div>
                <span>决策判断</span>
            </div>
        </div>

        <div class="diagram-container">
            <div class="mermaid">
flowchart TD
    %% 样式定义
    classDef initStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px,font-size:16px
    classDef sensorStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,font-size:16px
    classDef pidStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,font-size:16px
    classDef motorStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px,font-size:16px
    classDef decisionStyle fill:#ffebee,stroke:#c62828,stroke-width:2px,font-size:16px
    
    %% 主流程节点定义
    START([程序启动]):::initStyle
    INIT[系统初始化<br/>• PID_Init<br/>• SYSCFG_DL_init<br/>• 中断和定时器配置]:::initStyle
    MAIN_LOOP[主循环<br/>while 1 等待中断]:::initStyle
    
    %% 中断处理流程
    TIMER_INT[定时器中断<br/>TIMER_1_INST_IRQHandler]:::sensorStyle
    KEY_SCAN[按键扫描<br/>key_scan]:::sensorStyle
    CONTROL_CIRCLE[圈数控制<br/>control_cricle]:::sensorStyle
    GRAY_TASK[传感器读取<br/>Gray_Task]:::sensorStyle
    
    %% 传感器处理子流程
    subgraph SENSOR_PROCESS[传感器数据处理]
        READ_GPIO[读取8路GPIO<br/>DL_GPIO_readPins]:::sensorStyle
        PROCESS_DATA[数据处理<br/>• give_num<br/>• 数字量组合取反]:::sensorStyle
        CALC_ERROR[计算位置误差<br/>加权平均算法]:::sensorStyle
    end
    
    %% PID控制流程
    PID_TASK[PID控制任务<br/>PID_Task]:::pidStyle
    STATE_MACHINE{状态机判断<br/>turn_state}:::decisionStyle
    
    subgraph STATE_CONTROL[状态机控制]
        FIRST_STATE[FIRST状态<br/>等待第一个直角<br/>基础速度: 18]:::pidStyle
        NORMAL_STATE[NORMAL状态<br/>正常循迹<br/>基础速度: 30]:::pidStyle
        TURNING_STATE[TURNING状态<br/>转弯减速<br/>基础速度: 20]:::pidStyle
        RECOVER_STATE[RECOVER状态<br/>速度恢复<br/>延时100次]:::pidStyle
    end
    
    %% PID计算
    PID_CALC[PID计算<br/>• pid_calculate_positional<br/>• 输出限幅 ±100]:::pidStyle
    MOTOR_CALC[电机输出计算<br/>• 左轮 = 基础速度 - PID输出<br/>• 右轮 = 基础速度 + PID输出]:::pidStyle
    
    %% 电机控制
    SET_SPEED[设置电机速度<br/>Set_Speed<br/>限幅 -20~80]:::motorStyle
    MOTOR_CONTROL[电机驱动控制<br/>• PWM信号生成<br/>• 方向控制<br/>• 速度调节]:::motorStyle
    
    %% 状态转换条件
    LEFT_ANGLE{检测到<br/>左转角?}:::decisionStyle
    INNER_SENSOR{内侧传感器<br/>检测到黑线?}:::decisionStyle
    TIMER_CHECK{延时计数<br/>达到100?}:::decisionStyle
    
    %% 主流程连接
    START --> INIT
    INIT --> MAIN_LOOP
    MAIN_LOOP --> TIMER_INT
    
    TIMER_INT --> KEY_SCAN
    KEY_SCAN --> CONTROL_CIRCLE
    CONTROL_CIRCLE --> GRAY_TASK
    GRAY_TASK --> SENSOR_PROCESS
    
    %% 传感器处理流程
    SENSOR_PROCESS --> READ_GPIO
    READ_GPIO --> PROCESS_DATA
    PROCESS_DATA --> CALC_ERROR
    CALC_ERROR --> PID_TASK
    
    %% PID控制流程
    PID_TASK --> STATE_MACHINE
    STATE_MACHINE -->|FIRST| FIRST_STATE
    STATE_MACHINE -->|NORMAL| NORMAL_STATE
    STATE_MACHINE -->|TURNING| TURNING_STATE
    STATE_MACHINE -->|RECOVER| RECOVER_STATE
    
    %% 状态转换逻辑
    FIRST_STATE --> LEFT_ANGLE
    NORMAL_STATE --> LEFT_ANGLE
    LEFT_ANGLE -->|是| TURNING_STATE
    LEFT_ANGLE -->|否| PID_CALC
    
    TURNING_STATE --> INNER_SENSOR
    INNER_SENSOR -->|是| RECOVER_STATE
    INNER_SENSOR -->|否| PID_CALC
    
    RECOVER_STATE --> TIMER_CHECK
    TIMER_CHECK -->|是| NORMAL_STATE
    TIMER_CHECK -->|否| PID_CALC
    
    %% PID计算和电机控制
    PID_CALC --> MOTOR_CALC
    MOTOR_CALC --> SET_SPEED
    SET_SPEED --> MOTOR_CONTROL
    
    %% 循环回到主循环
    MOTOR_CONTROL --> MAIN_LOOP
            </div>
        </div>

        <div class="features">
            <div class="feature-card">
                <h3>核心功能模块</h3>
                <ul class="feature-list">
                    <li>系统初始化 - PID参数、系统配置、中断设置</li>
                    <li>传感器处理 - 8路GPIO读取、数据处理</li>
                    <li>PID控制 - 状态机管理、算法计算</li>
                    <li>电机控制 - 速度设置、PWM控制</li>
                    <li>用户交互 - 按键扫描、圈数控制</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>状态机设计</h3>
                <ul class="feature-list">
                    <li>FIRST - 初始状态，等待第一个直角</li>
                    <li>NORMAL - 正常循迹，基础速度30</li>
                    <li>TURNING - 转弯减速，基础速度20</li>
                    <li>RECOVER - 恢复状态，延时后恢复</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>技术特点</h3>
                <ul class="feature-list">
                    <li>中断驱动架构 - 高效实时控制</li>
                    <li>加权平均算法 - 精确位置计算</li>
                    <li>差速控制 - 左右轮独立调速</li>
                    <li>智能状态管理 - 处理复杂转弯</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>代码架构优势</h3>
                <ul class="feature-list">
                    <li>模块化设计 - 清晰的功能划分</li>
                    <li>可维护性强 - 良好的代码结构</li>
                    <li>实时性好 - 中断驱动响应快</li>
                    <li>扩展性强 - 易于功能扩展</li>
                </ul>
            </div>
        </div>

        <div class="print-info">
            <strong>💡 导出提示:</strong> 
            <br>• <strong>PDF格式:</strong> 使用浏览器打印功能，选择"另存为PDF"
            <br>• <strong>图片格式:</strong> 右键点击流程图区域，选择"另存为图像"
            <br>• <strong>最佳效果:</strong> 建议使用Chrome或Edge浏览器查看和导出
        </div>
    </div>

    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>