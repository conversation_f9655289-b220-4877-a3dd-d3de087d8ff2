# 25电赛E题循迹小车程序流程图技术架构设计

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-01-02
- **负责人**: Bob (架构师)
- **项目**: 循迹小车程序流程图技术实现架构

## 2. 架构设计概述

### 2.1 设计目标
基于Emma的PRD需求，设计一个清晰、专业的程序流程图，突出循迹小车的核心功能模块和执行逻辑。

### 2.2 技术选型
- **流程图工具**: Mermaid (支持代码化流程图生成)
- **输出格式**: HTML (可转换为PDF/JPG)
- **布局策略**: 垂直流程图，突出主要执行路径

## 3. 系统架构分析

### 3.1 代码架构分析

#### 3.1.1 主要模块划分
```
├── 系统初始化层
│   ├── PID_Init()           # PID参数初始化
│   ├── SYSCFG_DL_init()     # 系统配置初始化
│   └── 中断和定时器配置      # 硬件中断配置
│
├── 数据采集层
│   ├── Gray_Task()          # 8路灰度传感器读取
│   ├── give_num()           # 数字量处理
│   └── 加权平均计算          # 位置误差计算
│
├── 控制算法层
│   ├── PID_Task()           # PID控制主任务
│   ├── 状态机处理            # 转弯状态管理
│   └── pid_calculate_positional() # 位置式PID计算
│
├── 执行控制层
│   ├── Set_Speed()          # 电机速度设置
│   ├── Motor_On/Off()       # 电机开关控制
│   └── PWM信号生成          # 硬件PWM控制
│
└── 用户交互层
    ├── key_scan()           # 按键扫描
    └── control_cricle()     # 圈数控制
```

#### 3.1.2 执行流程分析
1. **主线程**: 初始化后进入无限循环等待中断
2. **中断线程**: 定时器中断驱动所有控制逻辑
3. **数据流**: 传感器 → PID计算 → 电机控制

### 3.2 状态机设计分析

#### 3.2.1 状态定义
```c
typedef enum{
    FIRST,    // 初始状态 - 等待第一个直角
    NORMAL,   // 正常循迹状态
    TURNING,  // 转弯状态 - 减速处理
    RECOVER   // 恢复状态 - 速度恢复
} TurnState_t;
```

#### 3.2.2 状态转换逻辑
- FIRST → TURNING: 检测到左转角标志
- NORMAL → TURNING: 检测到左转角标志  
- TURNING → RECOVER: 内侧传感器检测到黑线
- RECOVER → NORMAL: 延时后恢复正常速度

## 4. 流程图设计架构

### 4.1 整体布局设计

#### 4.1.1 层次化设计
```
┌─────────────────┐
│   系统启动层     │  ← 初始化模块
├─────────────────┤
│   主循环层       │  ← 程序主体结构
├─────────────────┤
│   中断处理层     │  ← 核心控制逻辑
├─────────────────┤
│   算法处理层     │  ← PID和状态机
├─────────────────┤
│   硬件控制层     │  ← 电机和传感器
└─────────────────┘
```

#### 4.1.2 流程图结构设计
- **主流程**: 垂直布局，从上到下展示执行顺序
- **分支流程**: 水平展开，展示条件判断和状态转换
- **模块分组**: 使用子图(subgraph)组织相关功能

### 4.2 Mermaid流程图代码架构

#### 4.2.1 基础语法选择
- 使用 `flowchart TD` (Top Down) 布局
- 节点形状: 矩形(process)、菱形(decision)、圆角矩形(start/end)
- 连接线: 实线(正常流程)、虚线(条件流程)

#### 4.2.2 样式设计
- **字体大小**: 14px (确保清晰可读)
- **节点颜色**: 功能模块使用不同颜色区分
- **线条粗细**: 2px (确保连接清晰)

## 5. 详细流程图设计

### 5.1 Mermaid代码结构

```mermaid
flowchart TD
    %% 样式定义
    classDef initStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px,font-size:14px
    classDef sensorStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,font-size:14px
    classDef pidStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,font-size:14px
    classDef motorStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px,font-size:14px
    classDef decisionStyle fill:#ffebee,stroke:#c62828,stroke-width:2px,font-size:14px
    
    %% 主流程节点定义
    START([程序启动]):::initStyle
    INIT[系统初始化<br/>PID_Init<br/>SYSCFG_DL_init<br/>中断配置]:::initStyle
    MAIN_LOOP[主循环<br/>while(1)]:::initStyle
    
    %% 中断处理流程
    TIMER_INT[定时器中断<br/>TIMER_1_INST_IRQHandler]:::sensorStyle
    KEY_SCAN[按键扫描<br/>key_scan]:::sensorStyle
    CONTROL_CIRCLE[圈数控制<br/>control_cricle]:::sensorStyle
    GRAY_TASK[传感器读取<br/>Gray_Task]:::sensorStyle
    
    %% 传感器处理子流程
    READ_GPIO[读取8路GPIO<br/>DL_GPIO_readPins]:::sensorStyle
    PROCESS_DATA[数据处理<br/>give_num<br/>数字量组合取反]:::sensorStyle
    CALC_ERROR[计算位置误差<br/>加权平均算法]:::sensorStyle
    
    %% PID控制流程
    PID_TASK[PID控制任务<br/>PID_Task]:::pidStyle
    STATE_MACHINE{状态机判断}:::decisionStyle
    FIRST_STATE[FIRST状态<br/>等待第一个直角]:::pidStyle
    NORMAL_STATE[NORMAL状态<br/>正常循迹]:::pidStyle
    TURNING_STATE[TURNING状态<br/>转弯减速]:::pidStyle
    RECOVER_STATE[RECOVER状态<br/>速度恢复]:::pidStyle
    
    %% PID计算
    PID_CALC[PID计算<br/>pid_calculate_positional<br/>输出限幅]:::pidStyle
    MOTOR_CALC[电机输出计算<br/>左右轮差速控制]:::pidStyle
    
    %% 电机控制
    SET_SPEED[设置电机速度<br/>Set_Speed<br/>PWM控制]:::motorStyle
    MOTOR_CONTROL[电机驱动<br/>方向控制<br/>速度调节]:::motorStyle
    
    %% 流程连接
    START --> INIT
    INIT --> MAIN_LOOP
    MAIN_LOOP --> TIMER_INT
    
    TIMER_INT --> KEY_SCAN
    KEY_SCAN --> CONTROL_CIRCLE
    CONTROL_CIRCLE --> GRAY_TASK
    GRAY_TASK --> PID_TASK
    
    %% 传感器处理流程
    GRAY_TASK --> READ_GPIO
    READ_GPIO --> PROCESS_DATA
    PROCESS_DATA --> CALC_ERROR
    
    %% PID控制流程
    PID_TASK --> STATE_MACHINE
    STATE_MACHINE -->|FIRST| FIRST_STATE
    STATE_MACHINE -->|NORMAL| NORMAL_STATE
    STATE_MACHINE -->|TURNING| TURNING_STATE
    STATE_MACHINE -->|RECOVER| RECOVER_STATE
    
    FIRST_STATE --> PID_CALC
    NORMAL_STATE --> PID_CALC
    TURNING_STATE --> PID_CALC
    RECOVER_STATE --> PID_CALC
    
    PID_CALC --> MOTOR_CALC
    MOTOR_CALC --> SET_SPEED
    SET_SPEED --> MOTOR_CONTROL
    
    %% 循环回到中断等待
    MOTOR_CONTROL --> MAIN_LOOP
```

### 5.2 技术实现细节

#### 5.2.1 关键技术点
1. **中断驱动架构**: 所有控制逻辑在定时器中断中执行
2. **状态机设计**: 处理直角转弯的复杂逻辑
3. **PID控制**: 位置式PID实现精确循迹
4. **差速控制**: 左右轮速度差实现转向

#### 5.2.2 算法优化点
1. **加权平均**: 8路传感器加权计算位置误差
2. **输出限幅**: 防止电机输出超出安全范围
3. **状态转换**: 智能处理直角转弯场景

## 6. 输出格式技术方案

### 6.1 HTML格式生成
- 使用Mermaid.js渲染引擎
- 支持交互式查看和缩放
- 字体清晰，适合在线展示

### 6.2 PDF转换方案
- 通过浏览器打印功能转换为PDF
- 保持矢量图形质量
- 适合正式文档提交

### 6.3 JPG导出方案
- 通过截图或导出功能生成
- 高分辨率确保清晰度
- 兼容性最强，便于分享

## 7. 质量保证措施

### 7.1 可读性保证
- 字体大小: 14px，确保清晰可读
- 颜色区分: 不同模块使用不同颜色
- 布局合理: 避免交叉连线，保持整洁

### 7.2 完整性保证
- 包含所有核心功能模块
- 体现完整的执行流程
- 突出关键的控制逻辑

### 7.3 专业性保证
- 符合软件工程流程图规范
- 体现良好的代码架构思维
- 适合向评委展示技术实现

## 8. 风险评估与缓解

### 8.1 技术风险
- **风险**: Mermaid渲染可能出现兼容性问题
- **缓解**: 提供多种格式备选方案

### 8.2 可读性风险
- **风险**: 流程图可能过于复杂
- **缓解**: 采用分层设计，突出主要流程

### 8.3 完整性风险
- **风险**: 可能遗漏关键功能点
- **缓解**: 基于代码分析确保覆盖完整

---

**架构设计状态**: ✅ 已完成
**下一步**: 转交Alex进行Mermaid代码实现和多格式文件生成