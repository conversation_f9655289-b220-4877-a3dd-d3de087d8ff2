################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
OLED_Hardware_SPI/%.o: ../OLED_Hardware_SPI/%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/Users/<USER>/workspace_ccstheia/my_project _02/App" -I"C:/Users/<USER>/workspace_ccstheia/my_project _02/Gray" -I"C:/Users/<USER>/workspace_ccstheia/my_project _02/PID" -I"C:/Users/<USER>/workspace_ccstheia/my_project _02/Driver" -I"C:/Users/<USER>/workspace_ccstheia/my_project _02/MSPM0" -I"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_SPI" -I"C:/Users/<USER>/workspace_ccstheia/my_project _02/MPU6050" -I"C:/Users/<USER>/workspace_ccstheia/my_project _02" -I"C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug" -I"E:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include" -I"E:/ti/mspm0_sdk_2_05_01_00/source" -DMPU6050 -DMONTION_DIVER_TARGET_MSP0 -gdwarf-3 -MMD -MP -MF"OLED_Hardware_SPI/$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


