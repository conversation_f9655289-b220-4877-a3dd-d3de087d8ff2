#include "ti_msp_dl_config.h"
#include "hardware_iic.h"
#include "gw_grayscale_sensor.h"

unsigned char Digtal; // ������
char Ch[256] = {0};
uint32_t new_num1; 
uint32_t new_num2; 
uint32_t new_num3; 
uint32_t new_num4; 
uint32_t new_num5; 
uint32_t new_num6; 
uint32_t new_num7; 
uint32_t new_num8; 
extern bool pid_running;
uint8_t var;
uint8_t per_var;
extern uint8_t count;

void key_scan();
void control_cricle();


void give_num(void)
{
  if (new_num1 == 0)
  {
    new_num1 = 0;
  }
  else 
  {
    new_num1 = 1;
  }

    if (new_num2 == 0)
  {
    new_num2 = 0;
  }
  else 
  {
    new_num2 = 1;
  }
    if (new_num3 == 0)
  {
    new_num3 = 0;
  }
  else 
  {
    new_num3 = 1;
  }
    if (new_num4 == 0)
  {
    new_num4 = 0;
  }
  else 
  {
    new_num4 = 1;
  }
  if (new_num5 == 0)
  {
    new_num5 = 0;
  }
  else 
  {
    new_num5 = 1;
  }
  if (new_num6 == 0)
  {
    new_num6 = 0;
  }
  else 
  {
    new_num6 = 1;
  }
  if (new_num7 == 0)
  {
    new_num7 = 0;
  }
  else 
  {
    new_num7 = 1;
  }

  if (new_num8 == 0)
  {
    new_num8 = 0;
  }
  else 
  {
    new_num8 = 1;
  }

}



float gray_weights[8] = {-9.0f, -6.0f, -3.0f, -1.0f, 1.0f, 3.0f, 5.0f, 8.0f}; // 8 ·�Ҷ�ͨ��Ȩ�ر�

float g_line_position_error; // ѭ�����ֵ

void Gray_Init(void)
{

}
bool g_is_left_angle = false;//左转判断标志位
void Gray_Task(void)
{

    uint32_t digit_1 = DL_GPIO_readPins(Gray_PIN_1_PORT,Gray_PIN_1_PIN); 
    uint32_t digit_2 = DL_GPIO_readPins(Gray_PIN_2_PORT,Gray_PIN_2_PIN); 
    uint32_t digit_3 = DL_GPIO_readPins(Gray_PIN_3_PORT,Gray_PIN_3_PIN); 
    uint32_t digit_4 = DL_GPIO_readPins(Gray_PIN_4_PORT,Gray_PIN_4_PIN); 
    uint32_t digit_5 = DL_GPIO_readPins(Gray_PIN_5_PORT,Gray_PIN_5_PIN); 
    uint32_t digit_6 = DL_GPIO_readPins(Gray_PIN_6_PORT,Gray_PIN_6_PIN);
    uint32_t digit_7 = DL_GPIO_readPins(Gray_PIN_7_PORT,Gray_PIN_7_PIN); 
    uint32_t digit_8 = DL_GPIO_readPins(Gray_PIN_8_PORT,Gray_PIN_8_PIN);

    new_num1 = DL_GPIO_readPins(Gray_PIN_2_PORT,Gray_PIN_2_PIN); 
    new_num2 = DL_GPIO_readPins(Gray_PIN_2_PORT,Gray_PIN_2_PIN); 
    new_num3 = DL_GPIO_readPins(Gray_PIN_3_PORT,Gray_PIN_3_PIN); 
    new_num4 = DL_GPIO_readPins(Gray_PIN_4_PORT,Gray_PIN_4_PIN);
    new_num5 = DL_GPIO_readPins(Gray_PIN_5_PORT,Gray_PIN_5_PIN); 
    new_num6 = DL_GPIO_readPins(Gray_PIN_6_PORT,Gray_PIN_6_PIN); 
    new_num7 = DL_GPIO_readPins(Gray_PIN_7_PORT,Gray_PIN_7_PIN); 
    new_num8  = DL_GPIO_readPins(Gray_PIN_8_PORT,Gray_PIN_8_PIN);

    give_num();
    

  if ((new_num8 == 1))
  {
    g_is_left_angle = true;
  }


    // 组合并取反
    unsigned char temp = 0;
    if (digit_1) temp |= (1 << 0);
    if (digit_2) temp |= (1 << 1);
    if (digit_3) temp |= (1 << 2);
    if (digit_4) temp |= (1 << 3);
    if (digit_5) temp |= (1 << 4);
    if (digit_6) temp |= (1 << 5);
    if (digit_7) temp |= (1 << 6);
    if (digit_8) temp |= (1 << 7);
    Digtal = ~temp;   // 这里完成取反

    //  Digtal = ~IIC_Get_Digtal();  // 或者用 MCU_GetDigital()

    // 打印8路数字量（保留）
    sprintf(Ch,"Digital %d-%d-%d-%d-%d-%d-%d-%d\r\n",
        (Digtal>>0)&0x01, (Digtal>>1)&0x01, (Digtal>>2)&0x01, (Digtal>>3)&0x01,
        (Digtal>>4)&0x01, (Digtal>>5)&0x01, (Digtal>>6)&0x01, (Digtal>>7)&0x01);

    // uart1_send_string(Ch);
    // uart1_send_char('\n');
  
    float weighted_sum = 0;
    uint8_t black_line_count = 0;

    for(uint8_t i = 0; i < 8; i++)
    {
      if((Digtal>>i) & 0x01)
      {
        weighted_sum += gray_weights[i];
        black_line_count++;
      }
    }
    
    if(black_line_count > 0)
      g_line_position_error = weighted_sum / (float)black_line_count;


    // key_scan();
    // control_cricle();

}

uint32_t count_button;
uint32_t B1_state;
uint32_t B1_last_state;
// uint32_t B2_state;
// uint32_t B2_last_state;
// uint8_t start_count ;



void key_scan()
{
	B1_state = DL_GPIO_readPins(GPIO_BUTTON_PORT ,GPIO_BUTTON_BUTTON_PIN);
	if(B1_state == 0 && B1_last_state != 0)   
	{
		count_button++;
    count_button = count_button % 6;

	}
	B1_last_state = B1_state;

  // B2_state =DL_GPIO_readPins(GPIO_St_PORT,GPIO_St_PIN_s_PIN);

// if(B2_state == 0 && B2_last_state != 0)
// 	{
// 		start_count ++;
//     start_count = start_count % 2;

// 	}
// 	B2_last_state = B2_state;
}
// void start_init()
// {
//     if (start_count == 1)
//     {
//       pid_running = true;
//     }
//   else 
//     {
//       pid_running = false;
//     }
// }


void control_cricle()
{

if(count_button == 1 && count == 4 )
{
    pid_running = false;
    Motor_Off();

}
if(count_button == 2 && count == 8)
{
  pid_running = false;
    Motor_Off();
}
if(count_button == 3 && count == 12)
{
  pid_running = false;
    Motor_Off();
}
if(count_button == 4 && count == 16)
{
  pid_running = false;
    Motor_Off();
}
if(count_button == 5 && count == 20)
{
  pid_running = false;
    Motor_Off();
}
}






















