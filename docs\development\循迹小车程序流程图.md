# 25电赛E题循迹小车程序流程图



## 流程图说明

### 核心功能模块
1. **系统初始化** (蓝色): PID参数初始化、系统配置、中断设置
2. **传感器处理** (紫色): 8路GPIO读取、数据处理、位置误差计算
3. **PID控制** (绿色): 状态机管理、PID计算、输出限幅
4. **电机控制** (橙色): 速度设置、PWM控制、方向控制
5. **决策判断** (红色): 条件判断和状态转换

### 状态机设计
- **FIRST**: 初始状态，等待第一个直角转弯
- **NORMAL**: 正常循迹状态，基础速度30
- **TURNING**: 转弯状态，减速至20
- **RECOVER**: 恢复状态，延时后恢复正常速度

### 关键技术点
- **中断驱动**: 定时器中断驱动所有控制逻辑
- **加权平均**: 8路传感器加权计算位置误差
- **差速控制**: 左右轮速度差实现精确转向
- **状态管理**: 智能处理直角转弯场景

## Mermaid源代码

```mermaid
flowchart TD
    %% 样式定义
    classDef initStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px,font-size:16px
    classDef sensorStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,font-size:16px
    classDef pidStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,font-size:16px
    classDef motorStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px,font-size:16px
    classDef decisionStyle fill:#ffebee,stroke:#c62828,stroke-width:2px,font-size:16px

    %% 主流程
    START([程序启动]):::initStyle
    INIT[系统初始化<br/>PID_Init<br/>SYSCFG_DL_init<br/>中断配置]:::initStyle
    MAIN_LOOP[主循环<br/>while 1]:::initStyle

    %% 中断处理
    TIMER_INT[定时器中断<br/>10ms周期]:::sensorStyle
    GRAY_TASK[传感器读取<br/>Gray_Task]:::sensorStyle
    READ_GPIO[读取8路GPIO]:::sensorStyle
    CALC_ERROR[计算位置误差<br/>加权平均]:::sensorStyle

    %% PID控制
    PID_TASK[PID控制<br/>PID_Task]:::pidStyle
    STATE_CHECK{状态判断}:::decisionStyle

    FIRST_STATE[FIRST状态<br/>速度18]:::pidStyle
    NORMAL_STATE[NORMAL状态<br/>速度30]:::pidStyle
    TURNING_STATE[TURNING状态<br/>速度20]:::pidStyle
    RECOVER_STATE[RECOVER状态<br/>延时恢复]:::pidStyle

    PID_CALC[PID计算<br/>位置式PID]:::pidStyle
    MOTOR_CALC[电机输出<br/>差速控制]:::pidStyle

    %% 电机控制
    SET_SPEED[设置电机速度<br/>PWM控制]:::motorStyle

    %% 状态转换
    LEFT_TURN{检测左转角?}:::decisionStyle
    INNER_DETECT{内侧检测?}:::decisionStyle
    TIME_CHECK{延时完成?}:::decisionStyle

    %% 主流程连接
    START --> INIT
    INIT --> MAIN_LOOP
    MAIN_LOOP --> TIMER_INT

    %% 中断处理流程
    TIMER_INT --> GRAY_TASK
    GRAY_TASK --> READ_GPIO
    READ_GPIO --> CALC_ERROR
    CALC_ERROR --> PID_TASK

    %% PID控制流程
    PID_TASK --> STATE_CHECK
    STATE_CHECK -->|FIRST| FIRST_STATE
    STATE_CHECK -->|NORMAL| NORMAL_STATE
    STATE_CHECK -->|TURNING| TURNING_STATE
    STATE_CHECK -->|RECOVER| RECOVER_STATE

    %% 状态转换逻辑
    FIRST_STATE --> LEFT_TURN
    NORMAL_STATE --> LEFT_TURN
    LEFT_TURN -->|是| TURNING_STATE
    LEFT_TURN -->|否| PID_CALC

    TURNING_STATE --> INNER_DETECT
    INNER_DETECT -->|是| RECOVER_STATE
    INNER_DETECT -->|否| PID_CALC

    RECOVER_STATE --> TIME_CHECK
    TIME_CHECK -->|是| NORMAL_STATE
    TIME_CHECK -->|否| PID_CALC

    %% 输出控制
    PID_CALC --> MOTOR_CALC
    MOTOR_CALC --> SET_SPEED
    SET_SPEED --> MAIN_LOOP
```

## 使用说明

### 查看方式
1. **在线查看**: 使用支持Mermaid的Markdown编辑器（如Typora、VS Code等）
2. **网页查看**: 复制代码到 https://mermaid.live/ 在线编辑器
3. **导出格式**: 可导出为PNG、SVG、PDF等格式

### 导出建议
- **PDF格式**: 适合正式文档提交，矢量图形不失真
- **PNG格式**: 适合插入PPT或Word文档
- **SVG格式**: 适合网页展示，支持缩放

## 技术特点展示

### 代码架构优势
1. **模块化设计**: 清晰的功能模块划分
2. **中断驱动**: 高效的实时控制架构
3. **状态机管理**: 智能的转弯处理逻辑
4. **PID控制**: 精确的循迹算法实现

### 功能完整性
- ✅ 系统初始化完整
- ✅ 传感器数据采集完整
- ✅ PID控制算法完整
- ✅ 电机驱动控制完整
- ✅ 用户交互功能完整

---

**文档生成**: Alex (工程师)  
**技术支持**: 基于Emma PRD需求和Bob架构设计  
**质量保证**: 字体清晰、逻辑完整、专业展示