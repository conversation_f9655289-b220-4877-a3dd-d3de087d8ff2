[{"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source\" -D_MSPM0G3507_ -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02/App/grap_app.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source\" -D_MSPM0G3507_ -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02/App/motor_app.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source\" -D_MSPM0G3507_ -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02/App/pid_app.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source\" -D_MSPM0G3507_ -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug/ti_msp_dl_config.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source\" -D_MSPM0G3507_ -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02/Driver/motor.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source\" -D_MSPM0G3507_ -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02/Gray/IIC.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source\" -D_MSPM0G3507_ -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02/Gray/Time.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source\" -D_MSPM0G3507_ -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02/Gray/hardware_iic.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source\" -D_MSPM0G3507_ -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02/MPU6050/inv_mpu.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source\" -D_MSPM0G3507_ -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02/MPU6050/inv_mpu_dmp_motion_driver.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source\" -D_MSPM0G3507_ -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02/MPU6050/mpu6050.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source\" -D_MSPM0G3507_ -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02/MPU6050/mspm0_i2c.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source\" -D_MSPM0G3507_ -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02/MSPM0/clock.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source\" -D_MSPM0G3507_ -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02/MSPM0/interrupt.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source\" -D_MSPM0G3507_ -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_I2C/oled_hardware_i2c.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source\" -D_MSPM0G3507_ -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02/PID/pid.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source\" -D_MSPM0G3507_ -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02/empty.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02/Debug\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"E:/ti/mspm0_sdk_2_05_01_00/source\" -D_MSPM0G3507_ -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02/uart_app.c"}]