#include "uart_app.h"

void uart1_send_char(char ch)
{
    // 等待发送缓冲区空闲
    while(DL_UART_isBusy(UART_OPENMV_INST) == true);
    // 发送单个字符
    DL_UART_Main_transmitData(UART_OPENMV_INST, ch);
}

void uart1_send_string(char* str)
{
    // 先判断字符串指针是否为空，避免空指针访问
    if(str == NULL)
        return;
    
    // 循环发送每个字符，直到遇到字符串结束符'\0'
    while(*str != '\0')
    {
        uart1_send_char(*str++);
    }
    
    // 可选：发送换行符，确保串口助手能正确换行显示
    uart1_send_char('\r');
    uart1_send_char('\n');
}