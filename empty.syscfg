/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3505" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4110"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const GPIO3   = GPIO.addInstance();
const GPIO4   = GPIO.addInstance();
const GPIO5   = GPIO.addInstance();
const GPIO6   = GPIO.addInstance();
const GPIO7   = GPIO.addInstance();
const I2C     = scripting.addModule("/ti/driverlib/I2C", {}, false);
const I2C1    = I2C.addInstance();
const I2C2    = I2C.addInstance();
const PWM     = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1    = PWM.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const TIMER   = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1  = TIMER.addInstance();
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
GPIO1.$name                         = "GPIO_MOTOR";
GPIO1.associatedPins.create(6);
GPIO1.associatedPins[0].$name       = "PIN_BL1";
GPIO1.associatedPins[0].pin.$assign = "PB3";
GPIO1.associatedPins[1].$name       = "PIN_BL2";
GPIO1.associatedPins[1].pin.$assign = "PA17";
GPIO1.associatedPins[2].$name       = "PIN_BR1";
GPIO1.associatedPins[2].pin.$assign = "PA24";
GPIO1.associatedPins[3].$name       = "PIN_BR2";
GPIO1.associatedPins[3].pin.$assign = "PA2";
GPIO1.associatedPins[4].$name       = "PIN_FSTBY";
GPIO1.associatedPins[4].pin.$assign = "PB19";
GPIO1.associatedPins[5].$name       = "PIN_BSTBY";
GPIO1.associatedPins[5].pin.$assign = "PB13";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.$name                              = "Gray";
GPIO2.associatedPins.create(8);
GPIO2.associatedPins[0].direction        = "INPUT";
GPIO2.associatedPins[0].internalResistor = "PULL_UP";
GPIO2.associatedPins[0].$name            = "PIN_1";
GPIO2.associatedPins[0].assignedPort     = "PORTA";
GPIO2.associatedPins[0].assignedPin      = "16";
GPIO2.associatedPins[1].direction        = "INPUT";
GPIO2.associatedPins[1].internalResistor = "PULL_UP";
GPIO2.associatedPins[1].$name            = "PIN_2";
GPIO2.associatedPins[1].assignedPort     = "PORTA";
GPIO2.associatedPins[1].assignedPin      = "15";
GPIO2.associatedPins[2].direction        = "INPUT";
GPIO2.associatedPins[2].internalResistor = "PULL_UP";
GPIO2.associatedPins[2].$name            = "PIN_3";
GPIO2.associatedPins[2].assignedPort     = "PORTA";
GPIO2.associatedPins[2].assignedPin      = "14";
GPIO2.associatedPins[3].$name            = "PIN_4";
GPIO2.associatedPins[3].direction        = "INPUT";
GPIO2.associatedPins[3].internalResistor = "PULL_UP";
GPIO2.associatedPins[3].assignedPort     = "PORTA";
GPIO2.associatedPins[3].assignedPin      = "13";
GPIO2.associatedPins[4].$name            = "PIN_5";
GPIO2.associatedPins[4].direction        = "INPUT";
GPIO2.associatedPins[4].internalResistor = "PULL_UP";
GPIO2.associatedPins[4].assignedPort     = "PORTA";
GPIO2.associatedPins[4].assignedPin      = "12";
GPIO2.associatedPins[5].$name            = "PIN_6";
GPIO2.associatedPins[5].direction        = "INPUT";
GPIO2.associatedPins[5].internalResistor = "PULL_UP";
GPIO2.associatedPins[5].assignedPort     = "PORTB";
GPIO2.associatedPins[5].assignedPin      = "16";
GPIO2.associatedPins[6].$name            = "PIN_7";
GPIO2.associatedPins[6].direction        = "INPUT";
GPIO2.associatedPins[6].internalResistor = "PULL_UP";
GPIO2.associatedPins[6].assignedPin      = "15";
GPIO2.associatedPins[6].assignedPort     = "PORTB";
GPIO2.associatedPins[7].$name            = "PIN_8";
GPIO2.associatedPins[7].direction        = "INPUT";
GPIO2.associatedPins[7].internalResistor = "PULL_UP";
GPIO2.associatedPins[7].assignedPort     = "PORTB";
GPIO2.associatedPins[7].assignedPin      = "17";

GPIO3.$name                         = "GPIO_OLED";
GPIO3.associatedPins[0].$name       = "PIN_RES";
GPIO3.associatedPins[0].pin.$assign = "PB20";

GPIO4.$name                         = "GPIO_MPU6050";
GPIO4.associatedPins[0].$name       = "PIN_INT";
GPIO4.associatedPins[0].direction   = "INPUT";
GPIO4.associatedPins[0].interruptEn = true;
GPIO4.associatedPins[0].polarity    = "RISE";
GPIO4.associatedPins[0].pin.$assign = "PB1";

GPIO5.$name                         = "OLED_GPIO";
GPIO5.associatedPins.create(2);
GPIO5.associatedPins[0].$name       = "OLED_SCL";
GPIO5.associatedPins[0].ioStructure = "OD";
GPIO5.associatedPins[1].$name       = "SDA";
GPIO5.associatedPins[1].ioStructure = "OD";
GPIO5.associatedPins[1].pin.$assign = "PA0";

GPIO6.$name                              = "GPIO_BUTTON";
GPIO6.associatedPins[0].$name            = "BUTTON";
GPIO6.associatedPins[0].assignedPort     = "PORTB";
GPIO6.associatedPins[0].assignedPin      = "21";
GPIO6.associatedPins[0].internalResistor = "PULL_UP";
GPIO6.associatedPins[0].direction        = "INPUT";

GPIO7.$name                              = "GPIO_St";
GPIO7.associatedPins[0].$name            = "PIN_s";
GPIO7.associatedPins[0].direction        = "INPUT";
GPIO7.associatedPins[0].internalResistor = "PULL_UP";
GPIO7.associatedPins[0].assignedPort     = "PORTA";
GPIO7.associatedPins[0].assignedPin      = "18";

I2C1.basicEnableController             = true;
I2C1.intController                     = ["ARBITRATION_LOST","NACK","RXFIFO_TRIGGER","RX_DONE","TX_DONE"];
I2C1.$name                             = "I2C_MPU6050";
I2C1.basicControllerStandardBusSpeed   = "Fast";
I2C1.peripheral.sdaPin.$assign         = "PA28";
I2C1.peripheral.sclPin.$assign         = "PA31";
I2C1.sdaPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sdaPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sdaPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sdaPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric6";
I2C1.sclPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sclPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sclPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sclPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric7";

I2C2.$name                             = "I2C_OLED";
I2C2.basicEnableController             = true;
I2C2.basicControllerStandardBusSpeed   = "Fast";
I2C2.peripheral.$assign                = "I2C1";
I2C2.peripheral.sdaPin.$assign         = "PA10";
I2C2.peripheral.sclPin.$assign         = "PA11";
I2C2.sdaPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C2.sdaPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C2.sdaPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C2.sdaPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric8";
I2C2.sclPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C2.sclPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C2.sclPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C2.sclPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric9";

PWM1.$name                              = "PWM_MOTOR";
PWM1.timerCount                         = 3200;
PWM1.ccIndex                            = [0,1,2,3];
PWM1.timerStartTimer                    = true;
PWM1.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_0.ccValue              = 3199;
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.PWM_CHANNEL_1.ccValue              = 3199;
PWM1.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric0";
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric1";
PWM1.peripheral.$assign                 = "TIMA0";
PWM1.peripheral.ccp0Pin.$assign         = "PA8";
PWM1.peripheral.ccp1Pin.$assign         = "PA9";
PWM1.peripheral.ccp2Pin.$assign         = "PB0";
PWM1.peripheral.ccp3Pin.$assign         = "PB2";
PWM1.PWM_CHANNEL_2.$name                = "ti_driverlib_pwm_PWMTimerCC2";
PWM1.PWM_CHANNEL_2.ccValue              = 3199;
PWM1.PWM_CHANNEL_3.$name                = "ti_driverlib_pwm_PWMTimerCC3";
PWM1.PWM_CHANNEL_3.ccValue              = 3199;
PWM1.ccp2PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric2";
PWM1.ccp3PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric3";

SYSCTL.forceDefaultClkConfig = true;

SYSTICK.periodEnable    = true;
SYSTICK.period          = 32000;
SYSTICK.interruptEnable = true;
SYSTICK.systickEnable   = true;

TIMER1.$name             = "TIMER_1";
TIMER1.timerPeriod       = "5ms";
TIMER1.timerClkPrescale  = 4;
TIMER1.timerMode         = "PERIODIC_UP";
TIMER1.interrupts        = ["ZERO"];
TIMER1.interruptPriority = "0";

UART1.$name                            = "UART_OPENMV";
UART1.enabledInterrupts                = ["RX"];
UART1.peripheral.rxPin.$assign         = "PB7";
UART1.peripheral.txPin.$assign         = "PB6";
UART1.txPinConfig.direction            = scripting.forceWrite("OUTPUT");
UART1.txPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART1.txPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART1.txPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART1.txPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric4";
UART1.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART1.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART1.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART1.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric5";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
Board.peripheral.$suggestSolution            = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution   = "PA20";
Board.peripheral.swdioPin.$suggestSolution   = "PA19";
GPIO2.associatedPins[0].pin.$suggestSolution = "PA16";
GPIO2.associatedPins[1].pin.$suggestSolution = "PA15";
GPIO2.associatedPins[2].pin.$suggestSolution = "PA14";
GPIO2.associatedPins[3].pin.$suggestSolution = "PA13";
GPIO2.associatedPins[4].pin.$suggestSolution = "PA12";
GPIO2.associatedPins[5].pin.$suggestSolution = "PB16";
GPIO2.associatedPins[6].pin.$suggestSolution = "PB15";
GPIO2.associatedPins[7].pin.$suggestSolution = "PB17";
GPIO5.associatedPins[0].pin.$suggestSolution = "PA1";
GPIO6.associatedPins[0].pin.$suggestSolution = "PB21";
GPIO7.associatedPins[0].pin.$suggestSolution = "PA18";
I2C1.peripheral.$suggestSolution             = "I2C0";
SYSCTL.peripheral.$suggestSolution           = "SYSCTL";
TIMER1.peripheral.$suggestSolution           = "TIMA1";
UART1.peripheral.$suggestSolution            = "UART1";
