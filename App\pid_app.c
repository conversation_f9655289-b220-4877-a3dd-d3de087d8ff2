#include "pid_app.h"
#include "motor.h"
#include "stdbool.h"
#include "uart_app.h"
#include "stdio.h"
void Gray_Task(void);
extern bool g_is_left_angle;
extern uint32_t new_num4;
extern uint32_t new_num5;
// /* PID 控制器实例 */
// PID_T pid_speed_left;  // 左轮速度环
// PID_T pid_speed_right; // 右轮速度环

PID_T pid_line;        // 循迹环


extern float g_line_position_error;
// void Line_PID_control(void) // 循迹环控制
// {
//   int line_pid_output = 0;
  
//   // 使用位置式 PID 计算利用循迹环计算输出
//   line_pid_output = pid_calculate_positional(&pid_line, g_line_position_error);
  
//   // 输出限幅
//   line_pid_output = pid_constrain(line_pid_output, pid_params_line.out_min, pid_params_line.out_max);
  
//   // 将差值作用在速度环的目标量上
//   pid_set_target(&pid_speed_left, basic_speed - line_pid_output);
//   pid_set_target(&pid_speed_right, basic_speed + line_pid_output);
// }

uint8_t count = 0;

extern uint32_t count_button;
PidParams_t pid_params_line = {
    .kp = 9.00f,        
    .ki = 0.001f,      
    .kd = 0.0f,      
    .out_min = -100.0f,
    .out_max = 100.0f,
};
void PID_Init(void)
{
  pid_init(&pid_line,
           pid_params_line.kp, pid_params_line.ki, pid_params_line.kd,
           0.0f, pid_params_line.out_max);
  
//   pid_init(&pid_angle,
//            pid_params_angle.kp, pid_params_angle.ki, pid_params_angle.kd,
//            0.0f, pid_params_angle.out_max);

  pid_set_target(&pid_line, 0);
}

bool pid_running = false; // PID 控制使能开关
int basic_speed = 10;// 基础速度


char str[64] = {0};
typedef enum{NORMAL,TURNING,RECOVER,FIRST} TurnState_t;
TurnState_t turn_state = FIRST;//状态机变量（全局）
uint32_t turn_timer = 0;

void 
PID_Task(void)
{
  //  if(pid_running == false) return;
 // 状态机处理
 switch (turn_state) {
     case FIRST:
         if (g_is_left_angle) {
             basic_speed = 18;       // 检测到直角时减速至50%
             turn_state = TURNING;   // 进入转弯状态
         }
         break;    
     case NORMAL:
         if (g_is_left_angle) {
             basic_speed = 20;       // 检测到直角时减速至50%
             turn_state = TURNING;   // 进入转弯状态
         }
         break;
         
     case TURNING:
         // 持续转弯直到内侧传感器回正（例如右侧传感器触发）
         if (new_num5) {        // 假设第5位（右侧）检测到黑线
             turn_state = RECOVER;
         }
         break;
         
     case RECOVER:
         // 保持低速1秒后恢复速度
          if (++turn_timer > 100) {
             basic_speed = 30;       // 恢复原速
             turn_state = NORMAL;
             count ++ ;
             turn_timer = 0;
          }
         break;
 }
    
    // ...原有PID计算和电机控制代码...

    float line_pid_output = 0.0f;
    int motor_left_output = 0, motor_right_output = 0;
    
    // 单环循迹PID控制
    line_pid_output = pid_calculate_positional(&pid_line, g_line_position_error);
    
    // 输出限幅
    line_pid_output = pid_constrain(line_pid_output, pid_params_line.out_min, pid_params_line.out_max);
    
    // 直接计算左右电机输出（基础速度 ± 循迹修正量）
    motor_left_output = basic_speed - (int)line_pid_output;   // 左转时减速
    motor_right_output = basic_speed + (int)line_pid_output;  // 右转时加速

      
    // sprintf(str,"%.1f",line_pid_output);
    // uart1_send_string(str);
    // DL_UART_Main_transmitData(UART_OPENMV_INST, (uint8_t)line_pid_output);
    
    // 电机输出限幅
    motor_left_output = pid_constrain(motor_left_output, -20, 80);
    motor_right_output = pid_constrain(motor_right_output, -20, 80);

    // 直接设置电机速度
    Set_Speed(0, motor_left_output);
    Set_Speed(1, motor_right_output);
}



// extern uint8_t start_count ;
void TIMER_1_INST_IRQHandler(void)
{
    key_scan();
    control_cricle();
    Gray_Task();
    
    PID_Task();

    // Set_Speed(0,20);
}

