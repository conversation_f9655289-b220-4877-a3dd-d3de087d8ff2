******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 18:29:43 2025

OUTPUT FILE NAME:   <my_project _02.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00002e5d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00008000  00003248  00004db8  R  X
  SRAM                  20200000   00004000  00000597  00003a69  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00003248   00003248    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00003020   00003020    r-x .text
  000030e0    000030e0    000000f8   000000f8    r-- .rodata
  000031d8    000031d8    00000070   00000070    r-- .cinit
20200000    20200000    00000397   00000000    rw-
  20200000    20200000    0000021c   00000000    rw- .bss
  2020021c    2020021c    0000017b   00000000    rw- .data
20203e00    20203e00    00000200   00000000    rw-
  20203e00    20203e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00003020     
                  000000c0    00000364     libc.a : e_asin.c.obj (.text.asin)
                  00000424    000002f8            : s_atan.c.obj (.text.atan)
                  0000071c    00000284            : _printfi.c.obj (.text:__TI_printfi_minimal)
                  000009a0    00000268     grap_app.o (.text.Gray_Task)
                  00000c08    00000210     mpu6050.o (.text.Read_Quad)
                  00000e18    000001a8     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00000fc0    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00001152    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00001154    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  000012dc    00000170            : e_sqrt.c.obj (.text.sqrt)
                  0000144c    00000158     mspm0_i2c.o (.text.mspm0_i2c_read)
                  000015a4    00000150     inv_mpu.o (.text.mpu_reset_fifo)
                  000016f4    00000130     mspm0_i2c.o (.text.mspm0_i2c_write)
                  00001824    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001930    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00001a34    000000f8     motor.o (.text.Set_Speed)
                  00001b2c    000000f4     pid_app.o (.text.PID_Task)
                  00001c20    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00001d08    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00001dec    000000e0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001ecc    000000dc     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_MOTOR_init)
                  00001fa8    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00002080    000000a4     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00002124    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  000021be    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000021c0    0000008c                            : mulsf3.S.obj (.text.__mulsf3)
                  0000224c    00000086     pid.o (.text.pid_calculate_positional)
                  000022d2    00000002     --HOLE-- [fill = 0]
                  000022d4    00000084     grap_app.o (.text.control_cricle)
                  00002358    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  000023da    00000002     --HOLE-- [fill = 0]
                  000023dc    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002458    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000024cc    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000024d0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00002544    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_OPENMV_init)
                  000025b4    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  0000261c    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00002680    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000026e2    00000002     --HOLE-- [fill = 0]
                  000026e4    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00002746    00000062     libc.a : memset16.S.obj (.text:memset)
                  000027a8    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002808    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00002866    00000002     --HOLE-- [fill = 0]
                  00002868    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000028bc    0000004c     empty.o (.text.main)
                  00002908    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00002950    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_1_init)
                  00002998    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  000029da    00000002     --HOLE-- [fill = 0]
                  000029dc    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002a1c    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00002a5c    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00002a9c    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00002ad8    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00002b14    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00002b50    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00002b8c    0000003c     grap_app.o (.text.key_scan)
                  00002bc8    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00002c04    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00002c3e    00000002     --HOLE-- [fill = 0]
                  00002c40    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00002c7a    00000002     --HOLE-- [fill = 0]
                  00002c7c    00000038     clock.o (.text.SysTick_Init)
                  00002cb4    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00002cec    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  00002d24    00000034     clock.o (.text.mspm0_delay_ms)
                  00002d58    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00002d88    0000002c     pid_app.o (.text.PID_Init)
                  00002db4    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00002de0    0000002a     pid.o (.text.pid_constrain)
                  00002e0a    00000002     --HOLE-- [fill = 0]
                  00002e0c    00000028     motor.o (.text.Motor_Off)
                  00002e34    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00002e5c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00002e84    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00002eaa    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00002ecc    00000020     driverlib.a : dl_uart.o (.text.DL_UART_transmitDataBlocking)
                  00002eec    0000001c                 : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00002f08    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00002f24    0000001c     pid.o (.text.pid_init)
                  00002f40    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00002f58    00000018     libc.a : sprintf.c.obj (.text._outs)
                  00002f70    00000018     clock.o (.text.mspm0_get_clock_ms)
                  00002f88    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00002f9e    00000002     --HOLE-- [fill = 0]
                  00002fa0    00000014     interrupt.o (.text.GROUP1_IRQHandler)
                  00002fb4    00000014     pid_app.o (.text.TIMA1_IRQHandler)
                  00002fc8    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00002fda    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  00002fec    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00002ffc    00000010     interrupt.o (.text.SysTick_Handler)
                  0000300c    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  0000301a    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00003028    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00003032    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000303c    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  0000304c    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00003056    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00003060    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  0000306a    00000002     --HOLE-- [fill = 0]
                  0000306c    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  0000307c    0000000a     libc.a : sprintf.c.obj (.text._outc)
                  00003086    00000008            : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  0000308e    00000002     --HOLE-- [fill = 0]
                  00003090    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00003098    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000030a0    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  000030a6    00000002     --HOLE-- [fill = 0]
                  000030a8    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  000030b8    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  000030be    00000006            : exit.c.obj (.text:abort)
                  000030c4    00000004            : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  000030c8    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000030cc    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000030d0    00000004     pid.o (.text.pid_set_target)
                  000030d4    0000000c     --HOLE-- [fill = 0]

.cinit     0    000031d8    00000070     
                  000031d8    0000004b     (.cinit..data.load) [load image, compression = lzss]
                  00003223    00000001     --HOLE-- [fill = 0]
                  00003224    0000000c     (__TI_handler_table)
                  00003230    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00003238    00000010     (__TI_cinit_table)

.rodata    0    000030e0    000000f8     
                  000030e0    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00003120    00000028     inv_mpu.o (.rodata.test)
                  00003148    00000022     grap_app.o (.rodata.str1.6241306812405096412.1)
                  0000316a    0000001e     inv_mpu.o (.rodata.reg)
                  00003188    00000014     ti_msp_dl_config.o (.rodata.gTIMER_1TimerConfig)
                  0000319c    00000011     libc.a : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  000031ad    00000001     --HOLE-- [fill = 0]
                  000031ae    0000000c     inv_mpu.o (.rodata.hw)
                  000031ba    0000000a     ti_msp_dl_config.o (.rodata.gUART_OPENMVConfig)
                  000031c4    00000008     ti_msp_dl_config.o (.rodata.gPWM_MOTORConfig)
                  000031cc    00000003     ti_msp_dl_config.o (.rodata.gPWM_MOTORClockConfig)
                  000031cf    00000003     ti_msp_dl_config.o (.rodata.gTIMER_1ClockConfig)
                  000031d2    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  000031d4    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  000031d6    00000002     ti_msp_dl_config.o (.rodata.gUART_OPENMVClockConfig)

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000021c     UNINITIALIZED
                  20200000    000000bc     (.common:gPWM_MOTORBackup)
                  202000bc    000000bc     (.common:gTIMER_1Backup)
                  20200178    0000003c     (.common:pid_line)
                  202001b4    00000010     (.common:quat)
                  202001c4    00000006     (.common:accel)
                  202001ca    00000006     (.common:gyro)
                  202001d0    00000004     (.common:B1_last_state)
                  202001d4    00000004     (.common:B1_state)
                  202001d8    00000004     (.common:count_button)
                  202001dc    00000004     (.common:g_line_position_error)
                  202001e0    00000004     (.common:new_num1)
                  202001e4    00000004     (.common:new_num2)
                  202001e8    00000004     (.common:new_num3)
                  202001ec    00000004     (.common:new_num4)
                  202001f0    00000004     (.common:new_num5)
                  202001f4    00000004     (.common:new_num6)
                  202001f8    00000004     (.common:new_num7)
                  202001fc    00000004     (.common:new_num8)
                  20200200    00000004     (.common:pitch)
                  20200204    00000004     (.common:roll)
                  20200208    00000004     (.common:sensor_timestamp)
                  2020020c    00000004     (.common:start_time)
                  20200210    00000004     (.common:tick_ms)
                  20200214    00000004     (.common:yaw)
                  20200218    00000002     (.common:sensors)
                  2020021a    00000001     (.common:Digtal)
                  2020021b    00000001     (.common:more)

.data      0    2020021c    0000017b     UNINITIALIZED
                  2020021c    00000100     grap_app.o (.data.Ch)
                  2020031c    0000002c     inv_mpu.o (.data.st)
                  20200348    00000020     grap_app.o (.data.gray_weights)
                  20200368    00000014     pid_app.o (.data.pid_params_line)
                  2020037c    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200380    00000004     pid_app.o (.data.basic_speed)
                  20200384    00000004     inv_mpu_dmp_motion_driver.o (.data.dmp.0)
                  20200388    00000004     inv_mpu_dmp_motion_driver.o (.data.dmp.1)
                  2020038c    00000004     pid_app.o (.data.turn_timer)
                  20200390    00000002     inv_mpu_dmp_motion_driver.o (.data.dmp.3)
                  20200392    00000001     pid_app.o (.data.count)
                  20200393    00000001     grap_app.o (.data.g_is_left_angle)
                  20200394    00000001     inv_mpu_dmp_motion_driver.o (.data.dmp.5)
                  20200395    00000001     pid_app.o (.data.pid_running)
                  20200396    00000001     pid_app.o (.data.turn_state)

.stack     0    20203e00    00000200     UNINITIALIZED
                  20203e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20203e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             1080    50        376    
       startup_mspm0g350x_ticlang.o   6       192       0      
       empty.o                        76      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1162    242       376    
                                                               
    .\App\
       grap_app.o                     808     34        338    
       pid_app.o                      308     0         91     
    +--+------------------------------+-------+---------+---------+
       Total:                         1116    34        429    
                                                               
    .\Driver\
       motor.o                        288     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         288     0         0      
                                                               
    .\MPU6050\
       mspm0_i2c.o                    648     0         0      
       inv_mpu.o                      500     82        44     
       mpu6050.o                      528     0         47     
       inv_mpu_dmp_motion_driver.o    424     0         11     
    +--+------------------------------+-------+---------+---------+
       Total:                         2100    82        102    
                                                               
    .\MSPM0\
       clock.o                        132     0         8      
       interrupt.o                    36      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         168     0         8      
                                                               
    .\PID\
       pid.o                          208     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         208     0         0      
                                                               
    E:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_i2c.o                       192     0         0      
       dl_uart.o                      122     0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         912     0         0      
                                                               
    E:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       _printfi.c.obj                 658     17        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       memcpy16.S.obj                 154     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       memset16.S.obj                 98      0         0      
       sprintf.c.obj                  90      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3768    81        4      
                                                               
    E:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    E:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               2       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2558    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       111       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   12284   550       1431   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00003238 records: 2, size/record: 8, table size: 16
	.data: load addr=000031d8, load size=0000004b bytes, run addr=2020021c, run size=0000017b bytes, compression=lzss
	.bss: load addr=00003230, load size=00000008 bytes, run addr=20200000, run size=0000021c bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00003224 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00001d09     0000303c     0000303a   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00003054          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             0000305e          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             0000308c          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             000030bc          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00001825     0000306c     00003068   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00000fcb     000030a8     000030a4   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             000030c6          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)

[3 trampolines]
[8 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
00001153  ADC0_IRQHandler                 
00001153  ADC1_IRQHandler                 
00001153  AES_IRQHandler                  
202001d0  B1_last_state                   
202001d4  B1_state                        
000024cc  C$$EXIT                         
00001153  CANFD0_IRQHandler               
2020021c  Ch                              
00001153  DAC0_IRQHandler                 
00003029  DL_Common_delayCycles           
00002809  DL_I2C_fillControllerTXFIFO     
00002a9d  DL_I2C_flushControllerTXFIFO    
00002e85  DL_I2C_setClockConfig           
00001931  DL_Timer_initFourCCPWMMode      
00001c21  DL_Timer_initTimerMode          
00002eed  DL_Timer_setCaptCompUpdateMethod
00002f41  DL_Timer_setCaptureCompareOutCtl
00002fed  DL_Timer_setCaptureCompareValue 
00002f09  DL_Timer_setClockConfig         
00002909  DL_UART_init                    
00002fc9  DL_UART_setClockConfig          
00002ecd  DL_UART_transmitDataBlocking    
00001153  DMA_IRQHandler                  
00001153  Default_Handler                 
2020021a  Digtal                          
00001153  GROUP0_IRQHandler               
00002fa1  GROUP1_IRQHandler               
000009a1  Gray_Task                       
000024cd  HOSTexit                        
00001153  HardFault_Handler               
00001153  I2C0_IRQHandler                 
00001153  I2C1_IRQHandler                 
00002e0d  Motor_Off                       
00001153  NMI_Handler                     
00002d89  PID_Init                        
00001b2d  PID_Task                        
00001153  PendSV_Handler                  
00001153  RTC_IRQHandler                  
00000c09  Read_Quad                       
000030c9  Reset_Handler                   
00001153  SPI0_IRQHandler                 
00001153  SPI1_IRQHandler                 
00001153  SVC_Handler                     
00001ded  SYSCFG_DL_GPIO_init             
0000261d  SYSCFG_DL_I2C_MPU6050_init      
00002869  SYSCFG_DL_I2C_OLED_init         
00001ecd  SYSCFG_DL_PWM_MOTOR_init        
000029dd  SYSCFG_DL_SYSCTL_init           
00002d59  SYSCFG_DL_SYSTICK_init          
00002951  SYSCFG_DL_TIMER_1_init          
00002545  SYSCFG_DL_UART_OPENMV_init      
00002ad9  SYSCFG_DL_init                  
000027a9  SYSCFG_DL_initPower             
00001a35  Set_Speed                       
00002ffd  SysTick_Handler                 
00002c7d  SysTick_Init                    
00001153  TIMA0_IRQHandler                
00002fb5  TIMA1_IRQHandler                
00001153  TIMG0_IRQHandler                
00001153  TIMG12_IRQHandler               
00001153  TIMG6_IRQHandler                
00001153  TIMG7_IRQHandler                
00001153  TIMG8_IRQHandler                
00001153  UART0_IRQHandler                
00001153  UART1_IRQHandler                
00001153  UART2_IRQHandler                
00001153  UART3_IRQHandler                
20204000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00003238  __TI_CINIT_Base                 
00003248  __TI_CINIT_Limit                
00003248  __TI_CINIT_Warm                 
00003224  __TI_Handler_Table_Base         
00003230  __TI_Handler_Table_Limit        
00002bc9  __TI_auto_init_nobinit_nopinit  
000023dd  __TI_decompress_lzss            
00002fdb  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
0000071d  __TI_printfi_minimal            
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
00002f89  __TI_zero_init_nomemset         
00000fcb  __adddf3                        
00001fb3  __addsf3                        
000024d1  __aeabi_d2f                     
00002999  __aeabi_d2uiz                   
00000fcb  __aeabi_dadd                    
00002681  __aeabi_dcmpeq                  
000026bd  __aeabi_dcmpge                  
000026d1  __aeabi_dcmpgt                  
000026a9  __aeabi_dcmple                  
00002695  __aeabi_dcmplt                  
00001825  __aeabi_ddiv                    
00001d09  __aeabi_dmul                    
00000fc1  __aeabi_dsub                    
2020037c  __aeabi_errno                   
00003091  __aeabi_errno_addr              
00002a5d  __aeabi_f2d                     
00002cb5  __aeabi_f2iz                    
00001fb3  __aeabi_fadd                    
000026e5  __aeabi_fcmpeq                  
00002721  __aeabi_fcmpge                  
00002735  __aeabi_fcmpgt                  
0000270d  __aeabi_fcmple                  
000026f9  __aeabi_fcmplt                  
00002359  __aeabi_fdiv                    
000021c1  __aeabi_fmul                    
00001fa9  __aeabi_fsub                    
00002db5  __aeabi_i2d                     
00002b15  __aeabi_i2f                     
000021bf  __aeabi_idiv0                   
00003099  __aeabi_memcpy                  
00003099  __aeabi_memcpy4                 
00003099  __aeabi_memcpy8                 
0000300d  __aeabi_memset                  
0000300d  __aeabi_memset4                 
0000300d  __aeabi_memset8                 
00002e35  __aeabi_ui2f                    
00002a1d  __aeabi_uidiv                   
00002a1d  __aeabi_uidivmod                
ffffffff  __binit__                       
000025b5  __cmpdf2                        
00002c05  __cmpsf2                        
00001825  __divdf3                        
00002359  __divsf3                        
000025b5  __eqdf2                         
00002c05  __eqsf2                         
00002a5d  __extendsfdf2                   
00002cb5  __fixsfsi                       
00002999  __fixunsdfsi                    
00002db5  __floatsidf                     
00002b15  __floatsisf                     
00002e35  __floatunsisf                   
00002459  __gedf2                         
00002b51  __gesf2                         
00002459  __gtdf2                         
00002b51  __gtsf2                         
000025b5  __ledf2                         
00002c05  __lesf2                         
000025b5  __ltdf2                         
00002c05  __ltsf2                         
UNDEFED   __mpu_init                      
00001d09  __muldf3                        
00002c41  __muldsi3                       
000021c1  __mulsf3                        
000025b5  __nedf2                         
00002c05  __nesf2                         
20203e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
00000fc1  __subdf3                        
00001fa9  __subsf3                        
000024d1  __truncdfsf2                    
00002e5d  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
000030cd  _system_pre_init                
000030bf  abort                           
202001c4  accel                           
000000c1  asin                            
000000c1  asinl                           
00000425  atan                            
00001155  atan2                           
00001155  atan2l                          
00000425  atanl                           
20200380  basic_speed                     
ffffffff  binit                           
000022d5  control_cricle                  
20200392  count                           
202001d8  count_button                    
00000e19  dmp_read_fifo                   
20200000  gPWM_MOTORBackup                
202000bc  gTIMER_1Backup                  
20200393  g_is_left_angle                 
202001dc  g_line_position_error           
20200348  gray_weights                    
202001ca  gyro                            
000031ae  hw                              
00000000  interruptVectors                
00002b8d  key_scan                        
000028bd  main                            
00002eab  memccpy                         
00002125  memcpy                          
00002747  memset                          
2020021b  more                            
00002081  mpu_read_fifo_stream            
000015a5  mpu_reset_fifo                  
00002d25  mspm0_delay_ms                  
00002f71  mspm0_get_clock_ms              
0000144d  mspm0_i2c_read                  
000016f5  mspm0_i2c_write                 
202001e0  new_num1                        
202001e4  new_num2                        
202001e8  new_num3                        
202001ec  new_num4                        
202001f0  new_num5                        
202001f4  new_num6                        
202001f8  new_num7                        
202001fc  new_num8                        
0000224d  pid_calculate_positional        
00002de1  pid_constrain                   
00002f25  pid_init                        
20200178  pid_line                        
20200368  pid_params_line                 
20200395  pid_running                     
000030d1  pid_set_target                  
20200200  pitch                           
202001b4  quat                            
0000316a  reg                             
20200204  roll                            
20200208  sensor_timestamp                
20200218  sensors                         
00002ced  sprintf                         
000012dd  sqrt                            
000012dd  sqrtl                           
2020020c  start_time                      
00003120  test                            
20200210  tick_ms                         
20200396  turn_state                      
2020038c  turn_timer                      
20200214  yaw                             


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  asin                            
000000c1  asinl                           
00000200  __STACK_SIZE                    
00000425  atan                            
00000425  atanl                           
0000071d  __TI_printfi_minimal            
000009a1  Gray_Task                       
00000c09  Read_Quad                       
00000e19  dmp_read_fifo                   
00000fc1  __aeabi_dsub                    
00000fc1  __subdf3                        
00000fcb  __adddf3                        
00000fcb  __aeabi_dadd                    
00001153  ADC0_IRQHandler                 
00001153  ADC1_IRQHandler                 
00001153  AES_IRQHandler                  
00001153  CANFD0_IRQHandler               
00001153  DAC0_IRQHandler                 
00001153  DMA_IRQHandler                  
00001153  Default_Handler                 
00001153  GROUP0_IRQHandler               
00001153  HardFault_Handler               
00001153  I2C0_IRQHandler                 
00001153  I2C1_IRQHandler                 
00001153  NMI_Handler                     
00001153  PendSV_Handler                  
00001153  RTC_IRQHandler                  
00001153  SPI0_IRQHandler                 
00001153  SPI1_IRQHandler                 
00001153  SVC_Handler                     
00001153  TIMA0_IRQHandler                
00001153  TIMG0_IRQHandler                
00001153  TIMG12_IRQHandler               
00001153  TIMG6_IRQHandler                
00001153  TIMG7_IRQHandler                
00001153  TIMG8_IRQHandler                
00001153  UART0_IRQHandler                
00001153  UART1_IRQHandler                
00001153  UART2_IRQHandler                
00001153  UART3_IRQHandler                
00001155  atan2                           
00001155  atan2l                          
000012dd  sqrt                            
000012dd  sqrtl                           
0000144d  mspm0_i2c_read                  
000015a5  mpu_reset_fifo                  
000016f5  mspm0_i2c_write                 
00001825  __aeabi_ddiv                    
00001825  __divdf3                        
00001931  DL_Timer_initFourCCPWMMode      
00001a35  Set_Speed                       
00001b2d  PID_Task                        
00001c21  DL_Timer_initTimerMode          
00001d09  __aeabi_dmul                    
00001d09  __muldf3                        
00001ded  SYSCFG_DL_GPIO_init             
00001ecd  SYSCFG_DL_PWM_MOTOR_init        
00001fa9  __aeabi_fsub                    
00001fa9  __subsf3                        
00001fb3  __addsf3                        
00001fb3  __aeabi_fadd                    
00002081  mpu_read_fifo_stream            
00002125  memcpy                          
000021bf  __aeabi_idiv0                   
000021c1  __aeabi_fmul                    
000021c1  __mulsf3                        
0000224d  pid_calculate_positional        
000022d5  control_cricle                  
00002359  __aeabi_fdiv                    
00002359  __divsf3                        
000023dd  __TI_decompress_lzss            
00002459  __gedf2                         
00002459  __gtdf2                         
000024cc  C$$EXIT                         
000024cd  HOSTexit                        
000024d1  __aeabi_d2f                     
000024d1  __truncdfsf2                    
00002545  SYSCFG_DL_UART_OPENMV_init      
000025b5  __cmpdf2                        
000025b5  __eqdf2                         
000025b5  __ledf2                         
000025b5  __ltdf2                         
000025b5  __nedf2                         
0000261d  SYSCFG_DL_I2C_MPU6050_init      
00002681  __aeabi_dcmpeq                  
00002695  __aeabi_dcmplt                  
000026a9  __aeabi_dcmple                  
000026bd  __aeabi_dcmpge                  
000026d1  __aeabi_dcmpgt                  
000026e5  __aeabi_fcmpeq                  
000026f9  __aeabi_fcmplt                  
0000270d  __aeabi_fcmple                  
00002721  __aeabi_fcmpge                  
00002735  __aeabi_fcmpgt                  
00002747  memset                          
000027a9  SYSCFG_DL_initPower             
00002809  DL_I2C_fillControllerTXFIFO     
00002869  SYSCFG_DL_I2C_OLED_init         
000028bd  main                            
00002909  DL_UART_init                    
00002951  SYSCFG_DL_TIMER_1_init          
00002999  __aeabi_d2uiz                   
00002999  __fixunsdfsi                    
000029dd  SYSCFG_DL_SYSCTL_init           
00002a1d  __aeabi_uidiv                   
00002a1d  __aeabi_uidivmod                
00002a5d  __aeabi_f2d                     
00002a5d  __extendsfdf2                   
00002a9d  DL_I2C_flushControllerTXFIFO    
00002ad9  SYSCFG_DL_init                  
00002b15  __aeabi_i2f                     
00002b15  __floatsisf                     
00002b51  __gesf2                         
00002b51  __gtsf2                         
00002b8d  key_scan                        
00002bc9  __TI_auto_init_nobinit_nopinit  
00002c05  __cmpsf2                        
00002c05  __eqsf2                         
00002c05  __lesf2                         
00002c05  __ltsf2                         
00002c05  __nesf2                         
00002c41  __muldsi3                       
00002c7d  SysTick_Init                    
00002cb5  __aeabi_f2iz                    
00002cb5  __fixsfsi                       
00002ced  sprintf                         
00002d25  mspm0_delay_ms                  
00002d59  SYSCFG_DL_SYSTICK_init          
00002d89  PID_Init                        
00002db5  __aeabi_i2d                     
00002db5  __floatsidf                     
00002de1  pid_constrain                   
00002e0d  Motor_Off                       
00002e35  __aeabi_ui2f                    
00002e35  __floatunsisf                   
00002e5d  _c_int00_noargs                 
00002e85  DL_I2C_setClockConfig           
00002eab  memccpy                         
00002ecd  DL_UART_transmitDataBlocking    
00002eed  DL_Timer_setCaptCompUpdateMethod
00002f09  DL_Timer_setClockConfig         
00002f25  pid_init                        
00002f41  DL_Timer_setCaptureCompareOutCtl
00002f71  mspm0_get_clock_ms              
00002f89  __TI_zero_init_nomemset         
00002fa1  GROUP1_IRQHandler               
00002fb5  TIMA1_IRQHandler                
00002fc9  DL_UART_setClockConfig          
00002fdb  __TI_decompress_none            
00002fed  DL_Timer_setCaptureCompareValue 
00002ffd  SysTick_Handler                 
0000300d  __aeabi_memset                  
0000300d  __aeabi_memset4                 
0000300d  __aeabi_memset8                 
00003029  DL_Common_delayCycles           
00003091  __aeabi_errno_addr              
00003099  __aeabi_memcpy                  
00003099  __aeabi_memcpy4                 
00003099  __aeabi_memcpy8                 
000030bf  abort                           
000030c9  Reset_Handler                   
000030cd  _system_pre_init                
000030d1  pid_set_target                  
00003120  test                            
0000316a  reg                             
000031ae  hw                              
00003224  __TI_Handler_Table_Base         
00003230  __TI_Handler_Table_Limit        
00003238  __TI_CINIT_Base                 
00003248  __TI_CINIT_Limit                
00003248  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gPWM_MOTORBackup                
202000bc  gTIMER_1Backup                  
20200178  pid_line                        
202001b4  quat                            
202001c4  accel                           
202001ca  gyro                            
202001d0  B1_last_state                   
202001d4  B1_state                        
202001d8  count_button                    
202001dc  g_line_position_error           
202001e0  new_num1                        
202001e4  new_num2                        
202001e8  new_num3                        
202001ec  new_num4                        
202001f0  new_num5                        
202001f4  new_num6                        
202001f8  new_num7                        
202001fc  new_num8                        
20200200  pitch                           
20200204  roll                            
20200208  sensor_timestamp                
2020020c  start_time                      
20200210  tick_ms                         
20200214  yaw                             
20200218  sensors                         
2020021a  Digtal                          
2020021b  more                            
2020021c  Ch                              
20200348  gray_weights                    
20200368  pid_params_line                 
2020037c  __aeabi_errno                   
20200380  basic_speed                     
2020038c  turn_timer                      
20200392  count                           
20200393  g_is_left_angle                 
20200395  pid_running                     
20200396  turn_state                      
20203e00  __stack                         
20204000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[232 symbols]
